document.addEventListener('DOMContentLoaded', function () {
    // Slideshow functionality
    function initSlideshow() {
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.slide-dot');
        let currentSlide = 0;

        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            slides[index].classList.add('active');
            dots[index].classList.add('active');
        }

        if (dots.length > 0) {
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentSlide = index;
                    showSlide(currentSlide);
                });
            });

            // Auto advance slides
            setInterval(() => {
                currentSlide = (currentSlide + 1) % slides.length;
                showSlide(currentSlide);
            }, 5000);
        }
    }

    initSlideshow();

    // Featured Promo Slideshow Navigation
    function initPromoSlideshow() {
        const promoSlides = document.querySelectorAll('.featured-promo .slide');
        const promoDots = document.querySelectorAll('.featured-promo .slide-dot');
        const prevBtn = document.querySelector('.featured-promo .prev-slide');
        const nextBtn = document.querySelector('.featured-promo .next-slide');
        let currentPromoSlide = 0;

        function showPromoSlide(index) {
            promoSlides.forEach(slide => slide.classList.remove('active'));
            promoDots.forEach(dot => dot.classList.remove('active'));

            if (promoSlides[index]) {
                promoSlides[index].classList.add('active');
            }
            if (promoDots[index]) {
                promoDots[index].classList.add('active');
            }
        }

        if (prevBtn && nextBtn && promoSlides.length > 0) {
            prevBtn.addEventListener('click', () => {
                currentPromoSlide = (currentPromoSlide - 1 + promoSlides.length) % promoSlides.length;
                showPromoSlide(currentPromoSlide);
            });

            nextBtn.addEventListener('click', () => {
                currentPromoSlide = (currentPromoSlide + 1) % promoSlides.length;
                showPromoSlide(currentPromoSlide);
            });

            // Dot navigation
            promoDots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentPromoSlide = index;
                    showPromoSlide(currentPromoSlide);
                });
            });
        }
    }

    initPromoSlideshow();

    // Form validation
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function (e) {
            e.preventDefault();
            // Add form validation logic here
        });
    }

    // Newsletter subscription
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function (e) {
            e.preventDefault();
            const emailInput = newsletterForm.querySelector('input[type="email"]');
            if (emailInput) {
                const emailValue = emailInput.value.trim();
                if (emailValue && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
                    // Handle newsletter subscription
                    alert('Thank you for subscribing to our newsletter!');
                    emailInput.value = '';
                } else {
                    alert('Please enter a valid email address.');
                }
            }
        });
    }

    // Mobile menu functionality
    const mobileMenuBtn = document.querySelector('.mobile-menu');
    const nav = document.querySelector('nav');
    const navLinks = document.querySelector('.nav-links');

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function () {
            this.classList.toggle('active');
            nav.style.left = nav.style.left === '0px' ? '-100%' : '0px';
        });

        // Close menu when clicking a link
        navLinks.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                nav.style.left = '-100%';
                mobileMenuBtn.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!nav.contains(e.target) && !mobileMenuBtn.contains(e.target) && nav.style.left === '0px') {
                nav.style.left = '-100%';
                mobileMenuBtn.classList.remove('active');
            }
        });
    }

    // Testimonial carousel functionality
    const prevTestimonialBtn = document.querySelector('.prev-testimonial');
    const nextTestimonialBtn = document.querySelector('.next-testimonial');

    if (prevTestimonialBtn && nextTestimonialBtn) {
        prevTestimonialBtn.addEventListener('click', () => {
            const slides = document.querySelectorAll('.testimonial-slide');
            const dots = document.querySelectorAll('.testimonial-dot');
            const active = document.querySelector('.testimonial-slide.active');

            if (slides.length > 0 && active) {
                const currentIndex = Array.from(slides).indexOf(active);
                const prevIndex = (currentIndex - 1 + slides.length) % slides.length;

                active.classList.remove('active');
                slides[prevIndex].classList.add('active');

                if (dots.length > 0) {
                    dots[currentIndex].classList.remove('active');
                    dots[prevIndex].classList.add('active');
                }
            }
        });

        nextTestimonialBtn.addEventListener('click', () => {
            const slides = document.querySelectorAll('.testimonial-slide');
            const dots = document.querySelectorAll('.testimonial-dot');
            const active = document.querySelector('.testimonial-slide.active');

            if (slides.length > 0 && active) {
                const currentIndex = Array.from(slides).indexOf(active);
                const nextIndex = (currentIndex + 1) % slides.length;

                active.classList.remove('active');
                slides[nextIndex].classList.add('active');

                if (dots.length > 0) {
                    dots[currentIndex].classList.remove('active');
                    dots[nextIndex].classList.add('active');
                }
            }
        });
    }

    // Back to Top Button Functionality
    function createBackToTopButton() {
        // Check if button already exists
        if (document.querySelector('.back-to-top')) {
            return;
        }

        // Create the button element
        const backToTopButton = document.createElement('div');
        backToTopButton.className = 'back-to-top';
        backToTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        backToTopButton.setAttribute('title', 'Back to Top');

        // Append to body
        document.body.appendChild(backToTopButton);

        // Show/hide button based on scroll position
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('active');
            } else {
                backToTopButton.classList.remove('active');
            }
        });

        // Scroll to top when clicked
        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        console.log('Back to top button created successfully');
    }

    // Create the back to top button
    createBackToTopButton();

    // Catalogue Page Functionality - Updated to work with sidebar filtering
    const setupCatalogueFilters = () => {
        console.log('Setting up catalogue filters...');

        // Function to filter products by category
        window.filterProductsByCategory = (selectedCategory) => {
            console.log('Filtering by category:', selectedCategory);

            // Get all product cards
            const productCards = document.querySelectorAll('.product-card');
            let visibleCount = 0;

            productCards.forEach(card => {
                const cardCategory = card.getAttribute('data-category');

                if (selectedCategory === 'all' || cardCategory === selectedCategory) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Show/hide no results message
            const noResultsMessage = document.getElementById('no-results-message');
            if (noResultsMessage) {
                if (visibleCount === 0 && selectedCategory !== 'all') {
                    noResultsMessage.style.display = 'block';
                } else {
                    noResultsMessage.style.display = 'none';
                }
            }

            console.log(`Filtered results: ${visibleCount} products visible`);
        };

        // Initialize with all products visible
        window.filterProductsByCategory('all');
    };

    // Initialize catalogue filters if on catalogue page
    if (document.querySelector('.catalogue-page')) {
        try {
            console.log('Catalogue page detected, setting up filters...');
            setupCatalogueFilters();

            // Handle URL fragment for category filtering
            const handleUrlFragment = () => {
                const hash = window.location.hash.substring(1); // Remove the # symbol
                if (hash) {
                    console.log('URL fragment detected:', hash);

                    // Find the corresponding category link and activate it
                    const categoryLinks = document.querySelectorAll('.category-link');
                    categoryLinks.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('data-category') === hash) {
                            link.classList.add('active');
                        }
                    });

                    // Filter products by the hash category
                    if (window.filterProductsByCategory) {
                        window.filterProductsByCategory(hash);
                    }
                } else {
                    // No hash, show all categories
                    window.filterProductsByCategory('all');
                }
            };

            // Handle initial page load with fragment
            handleUrlFragment();

            // Handle hash changes (if user navigates with browser back/forward)
            window.addEventListener('hashchange', handleUrlFragment);

            console.log('Catalogue filters setup completed');
        } catch (error) {
            console.error('Error setting up catalogue filters:', error);
        }
    }

    // Product Details Modal System
    const productData = {
        'modern-sofa': {
            name: 'Modern Sofa',
            category: 'Living Room',
            price: 'GH₵ 2,500 - GH₵ 3,200',
            image: 'images/modern-sofa.jpg',
            description: 'Elegant design with premium upholstery and solid wood frame.',
            features: [
                'Premium fabric upholstery',
                'Solid hardwood frame',
                'High-density foam cushions',
                'Available in multiple colors',
                'Easy to clean and maintain',
                '3-year warranty included'
            ],
            specifications: {
                'Dimensions': '200cm x 90cm x 85cm',
                'Material': 'Hardwood frame with fabric upholstery',
                'Weight': '45kg',
                'Seating Capacity': '3 people',
                'Color Options': 'Brown, Grey, Navy Blue, Beige'
            }
        },
        'luxury-sofa-set': {
            name: 'Luxury Sofa Set',
            category: 'Living Room',
            price: 'GH₵ 4,800 - GH₵ 6,500',
            image: 'images/luxury-sofa-set.jpg',
            description: 'Premium 3-seater sofa with matching armchairs in elegant fabric upholstery.',
            features: [
                'Complete 3-piece set',
                'Premium leather upholstery',
                'Ergonomic design',
                'Matching coffee table available',
                'Professional delivery and setup',
                '5-year warranty'
            ],
            specifications: {
                'Set Includes': '1 Three-seater sofa + 2 Armchairs',
                'Dimensions': 'Sofa: 220cm x 95cm x 90cm',
                'Material': 'Genuine leather with hardwood frame',
                'Weight': '120kg (complete set)',
                'Color Options': 'Black, Brown, Burgundy'
            }
        },
        'royal-elegance-sofa-set': {
            name: 'Royal Elegance Sofa Set',
            category: 'Living Room',
            price: 'GH₵ 7,200 - GH₵ 9,800',
            image: 'images/royal-elegance-sofa--set.webp',
            description: 'Luxurious 5-piece set with premium leather upholstery and solid wood frame.',
            features: [
                'Luxurious 5-piece furniture set',
                'Premium Italian leather',
                'Hand-crafted solid wood frame',
                'Adjustable headrests',
                'Built-in cup holders',
                'Lifetime frame warranty'
            ],
            specifications: {
                'Set Includes': '1 Three-seater + 2 Two-seaters + 2 Armchairs',
                'Dimensions': 'Main sofa: 240cm x 100cm x 95cm',
                'Material': 'Italian leather with mahogany frame',
                'Weight': '200kg (complete set)',
                'Color Options': 'Rich Brown, Classic Black'
            }
        },
        'luxury-bed-frame': {
            name: 'Luxury Bed Frame',
            category: 'Bedroom',
            price: 'GH₵ 1,800 - GH₵ 2,800',
            image: 'images/king-size-bed.jpg',
            description: 'Premium king-size bed frame with headboard and storage.',
            features: [
                'King-size dimensions',
                'Built-in storage drawers',
                'Upholstered headboard',
                'Solid wood construction',
                'Easy assembly',
                '2-year warranty'
            ],
            specifications: {
                'Dimensions': '200cm x 180cm x 120cm',
                'Material': 'Solid wood with fabric headboard',
                'Storage': '4 under-bed drawers',
                'Weight': '85kg',
                'Color Options': 'Walnut, Oak, Mahogany'
            }
        },
        'dining-table-set': {
            name: 'Dining Table Set',
            category: 'Dining',
            price: 'GH₵ 3,200 - GH₵ 4,500',
            image: 'images/dining-set.webp',
            description: 'Contemporary dining set with 6 chairs and extendable table.',
            features: [
                '6-seater dining set',
                'Extendable table design',
                'Comfortable padded chairs',
                'Scratch-resistant surface',
                'Modern contemporary style',
                '3-year warranty'
            ],
            specifications: {
                'Set Includes': '1 Table + 6 Chairs',
                'Table Dimensions': '160cm x 90cm (extends to 200cm)',
                'Material': 'Solid wood with glass top',
                'Chair Material': 'Wood frame with fabric cushions',
                'Color Options': 'Natural Wood, Dark Walnut'
            }
        },
        'office-desk': {
            name: 'Office Desk',
            category: 'Office',
            price: 'GH₵ 1,200 - GH₵ 1,800',
            image: 'images/office-desk.jpg',
            description: 'Ergonomic office desk with cable management and drawers.',
            features: [
                'Ergonomic design',
                'Built-in cable management',
                'Multiple storage drawers',
                'Scratch-resistant surface',
                'Modern professional look',
                '2-year warranty'
            ],
            specifications: {
                'Dimensions': '140cm x 70cm x 75cm',
                'Material': 'Engineered wood with laminate finish',
                'Storage': '3 drawers + 1 cabinet',
                'Weight': '35kg',
                'Color Options': 'White, Black, Walnut'
            }
        },
        'outdoor-dining-set': {
            name: 'Outdoor Dining Set',
            category: 'Outdoor',
            price: 'GH₵ 2,800 - GH₵ 3,800',
            image: 'images/outdoor.jpg',
            description: 'Weather-resistant dining set perfect for patios and gardens.',
            features: [
                'Weather-resistant materials',
                'UV protection coating',
                '4-seater outdoor set',
                'Easy to clean',
                'Rust-proof hardware',
                '3-year weather warranty'
            ],
            specifications: {
                'Set Includes': '1 Table + 4 Chairs',
                'Table Dimensions': '120cm x 80cm x 75cm',
                'Material': 'Aluminum frame with textilene fabric',
                'Weight': '25kg (complete set)',
                'Color Options': 'Grey, Brown, Black'
            }
        },
        'kitchen-island': {
            name: 'Kitchen Island',
            category: 'Kitchen',
            price: 'GH₵ 2,200 - GH₵ 3,000',
            image: 'images/kitchen.jpg',
            description: 'Versatile kitchen island with storage and workspace.',
            features: [
                'Large workspace surface',
                'Multiple storage compartments',
                'Built-in spice rack',
                'Towel holders',
                'Easy-clean surface',
                '2-year warranty'
            ],
            specifications: {
                'Dimensions': '150cm x 60cm x 90cm',
                'Material': 'Solid wood with granite top',
                'Storage': '4 drawers + 2 cabinets',
                'Weight': '75kg',
                'Color Options': 'Natural Wood, White, Dark Oak'
            }
        },
        'auditorium-seating': {
            name: 'Auditorium Seating',
            category: 'Auditorium & Classrooms',
            price: 'GH₵ 450 - GH₵ 650 per seat',
            image: 'images/auditorium.jpg',
            description: 'Comfortable and durable seating for auditoriums and lecture halls.',
            features: [
                'Ergonomic design',
                'Fold-up seat mechanism',
                'Cup holder included',
                'Fire-resistant materials',
                'Easy maintenance',
                '5-year warranty'
            ],
            specifications: {
                'Seat Width': '55cm',
                'Material': 'Steel frame with fabric upholstery',
                'Weight': '18kg per seat',
                'Installation': 'Floor-mounted',
                'Color Options': 'Blue, Red, Grey, Black'
            }
        }
    };

    // Create and setup product details modal
    function createProductModal() {
        if (document.getElementById('product-modal')) return;

        const modalHTML = `
            <div id="product-modal" class="product-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="modal-product-name"></h2>
                        <span class="modal-close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="modal-image">
                            <img id="modal-product-image" src="" alt="">
                        </div>
                        <div class="modal-details">
                            <div class="product-category">
                                <span id="modal-product-category"></span>
                            </div>
                            <div class="product-price">
                                <span id="modal-product-price"></span>
                            </div>
                            <div class="product-description">
                                <p id="modal-product-description"></p>
                            </div>
                            <div class="product-features">
                                <h3>Key Features</h3>
                                <ul id="modal-product-features"></ul>
                            </div>
                            <div class="product-specifications">
                                <h3>Specifications</h3>
                                <div id="modal-product-specs"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <a href="contact.html" class="btn-primary">Contact Us</a>
                        <a href="catalogue.html" class="btn-secondary">View More Products</a>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        setupModalEvents();
    }

    // Setup modal event handlers
    function setupModalEvents() {
        const modal = document.getElementById('product-modal');
        const closeBtn = document.querySelector('.modal-close');

        // Close modal when clicking X
        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'block') {
                modal.style.display = 'none';
            }
        });
    }

    // Show product details in modal
    function showProductDetails(productKey) {
        const product = productData[productKey];
        if (!product) {
            console.error('Product not found:', productKey);
            return;
        }

        // Populate modal with product data
        document.getElementById('modal-product-name').textContent = product.name;
        document.getElementById('modal-product-category').textContent = product.category;
        document.getElementById('modal-product-price').textContent = product.price;
        document.getElementById('modal-product-description').textContent = product.description;
        document.getElementById('modal-product-image').src = product.image;
        document.getElementById('modal-product-image').alt = product.name;

        // Populate features
        const featuresList = document.getElementById('modal-product-features');
        featuresList.innerHTML = '';
        product.features.forEach(feature => {
            const li = document.createElement('li');
            li.textContent = feature;
            featuresList.appendChild(li);
        });

        // Populate specifications
        const specsList = document.getElementById('modal-product-specs');
        specsList.innerHTML = '';
        Object.entries(product.specifications).forEach(([key, value]) => {
            const specItem = document.createElement('div');
            specItem.className = 'spec-item';
            specItem.innerHTML = `<strong>${key}:</strong> ${value}`;
            specsList.appendChild(specItem);
        });

        // Show modal
        document.getElementById('product-modal').style.display = 'block';
    }

    // Setup product detail links
    function setupProductLinks() {
        // Create modal first
        createProductModal();

        // Setup click handlers for all "View Details" buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-tertiary') && e.target.textContent.trim() === 'View Details') {
                e.preventDefault();

                // Get product key from the product card
                const productCard = e.target.closest('.product-card');
                if (productCard) {
                    const productName = productCard.querySelector('h3').textContent.trim();
                    const productKey = getProductKey(productName);

                    if (productKey && productData[productKey]) {
                        showProductDetails(productKey);
                    } else {
                        // Fallback for products without detailed data
                        alert('Product details coming soon! Please contact us for more information.');
                    }
                }
            }
        });
    }

    // Convert product name to product key
    function getProductKey(productName) {
        const keyMap = {
            'Modern Sofa': 'modern-sofa',
            'Luxury Sofa Set': 'luxury-sofa-set',
            'Royal Elegance Sofa Set': 'royal-elegance-sofa-set',
            'Luxury Bed Frame': 'luxury-bed-frame',
            'Dining Table Set': 'dining-table-set',
            'Office Desk': 'office-desk',
            'Outdoor Dining Set': 'outdoor-dining-set',
            'Kitchen Island': 'kitchen-island',
            'Auditorium Seating': 'auditorium-seating'
        };
        return keyMap[productName] || null;
    }

    // Initialize product details system
    setupProductLinks();

    // Catalogue Sidebar Functionality
    function setupCatalogueSidebar() {
        const categoryLinks = document.querySelectorAll('.category-link');
        const mobileSidebarBtn = document.getElementById('mobile-sidebar-btn');
        const sidebar = document.querySelector('.catalogue-sidebar');

        // Create overlay for mobile
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        document.body.appendChild(overlay);

        // Category filtering
        categoryLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                // Remove active class from all links
                categoryLinks.forEach(l => l.classList.remove('active'));

                // Add active class to clicked link
                link.classList.add('active');

                // Get category
                const category = link.getAttribute('data-category');

                // Update URL hash
                if (category === 'all') {
                    // Remove hash for "all categories"
                    history.pushState(null, null, window.location.pathname);
                } else {
                    // Set hash for specific category
                    window.location.hash = category;
                }

                // Use the new filtering function
                if (window.filterProductsByCategory) {
                    window.filterProductsByCategory(category);
                }

                // Close mobile sidebar if open
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }

                // Scroll to top - if footer link, scroll to very top, otherwise scroll to main content
                if (link.closest('footer')) {
                    // Footer category link - scroll to top of page
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                } else {
                    // Sidebar category link - scroll to main content
                    document.querySelector('.catalogue-main').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile sidebar toggle
        if (mobileSidebarBtn) {
            mobileSidebarBtn.addEventListener('click', () => {
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
            });
        }

        // Close sidebar when clicking overlay
        overlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        });

        // Close sidebar on window resize if mobile view is exited
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
            }
        });
    }

    // Image Lightbox Functionality
    function setupImageLightbox() {
        const lightbox = document.getElementById('image-lightbox');
        const lightboxImage = document.getElementById('lightbox-image');
        const lightboxClose = document.querySelector('.lightbox-close');

        if (!lightbox || !lightboxImage || !lightboxClose) return;

        // Add click event to all product images
        document.addEventListener('click', (e) => {
            if (e.target.matches('.product-image img')) {
                e.preventDefault();
                lightboxImage.src = e.target.src;
                lightboxImage.alt = e.target.alt || 'Product Image';
                lightbox.style.display = 'block';
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            }
        });

        // Close lightbox when clicking the close button
        lightboxClose.addEventListener('click', () => {
            lightbox.style.display = 'none';
            document.body.style.overflow = 'auto';
        });

        // Close lightbox when clicking outside the image
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });

        // Close lightbox with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && lightbox.style.display === 'block') {
                lightbox.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    }

    // Initialize catalogue sidebar if on catalogue page
    if (document.querySelector('.catalogue-page')) {
        setupCatalogueSidebar();
        setupImageLightbox();
    }
});